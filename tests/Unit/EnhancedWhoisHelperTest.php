<?php

namespace Tests\Unit;

use App\Helpers\EnhancedWhoisHelper;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class EnhancedWhoisHelperTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        // Clear cache before each test
        Cache::forget('iana_whois_servers');
    }

    /**
     * Test that custom TLD servers are properly configured.
     */
    public function test_custom_tld_servers_configuration(): void
    {
        $customServers = EnhancedWhoisHelper::getCustomTldServers();

        $this->assertIsArray($customServers);
        $this->assertArrayHasKey('.pk', $customServers);
        $this->assertArrayHasKey('.in', $customServers);
        $this->assertEquals('whois.pknic.net.pk', $customServers['.pk']);
        $this->assertEquals('whois.registry.in', $customServers['.in']);
    }

    /**
     * Test adding custom TLD server at runtime.
     */
    public function test_add_custom_tld_server(): void
    {
        $originalServers = EnhancedWhoisHelper::getCustomTldServers();

        EnhancedWhoisHelper::addCustomTldServer('.test', 'whois.test.example');

        $updatedServers = EnhancedWhoisHelper::getCustomTldServers();
        $this->assertArrayHasKey('.test', $updatedServers);
        $this->assertEquals('whois.test.example', $updatedServers['.test']);

        // Verify original servers are still there
        foreach ($originalServers as $tld => $host) {
            $this->assertArrayHasKey($tld, $updatedServers);
            $this->assertEquals($host, $updatedServers[$tld]);
        }
    }

    /**
     * Test WHOIS instance creation with custom servers.
     */
    public function test_create_whois_instance(): void
    {
        // Mock HTTP responses for IANA lookups
        Http::fake([
            'https://www.iana.org/domains/root/db/pk.html' => Http::response($this->getMockIanaResponse('.pk', 'whois.pknic.net.pk')),
            'https://www.iana.org/domains/root/db/in.html' => Http::response($this->getMockIanaResponse('.in', 'whois.registry.in')),
            'https://www.iana.org/domains/root/db/*' => Http::response('Not found', 404),
        ]);

        $whois = EnhancedWhoisHelper::createWhoisInstance();

        $this->assertInstanceOf(\Iodev\Whois\Whois::class, $whois);

        // Verify that custom servers are available
        $availableServers = EnhancedWhoisHelper::getAvailableTldServers();
        $this->assertIsArray($availableServers);
    }

    /**
     * Test IANA cache functionality.
     */
    public function test_iana_cache_functionality(): void
    {
        // Mock successful IANA response
        Http::fake([
            'https://www.iana.org/domains/root/db/pk.html' => Http::response($this->getMockIanaResponse('.pk', 'whois.pknic.net.pk')),
            'https://www.iana.org/domains/root/db/*' => Http::response('Not found', 404),
        ]);

        // First call should make HTTP request
        $whois1 = EnhancedWhoisHelper::createWhoisInstance();

        // Verify cache was set
        $this->assertTrue(Cache::has('iana_whois_servers'));

        // Second call should use cache (no HTTP request)
        Http::fake(); // Reset HTTP fake to ensure no requests are made
        $whois2 = EnhancedWhoisHelper::createWhoisInstance();

        $this->assertInstanceOf(\Iodev\Whois\Whois::class, $whois2);
    }

    /**
     * Test cache clearing functionality.
     */
    public function test_clear_iana_cache(): void
    {
        // Set some cache data
        Cache::put('iana_whois_servers', ['.test' => 'whois.test.example'], 3600);
        $this->assertTrue(Cache::has('iana_whois_servers'));

        // Clear cache
        EnhancedWhoisHelper::clearIanaCache();

        // Verify cache is cleared
        $this->assertFalse(Cache::has('iana_whois_servers'));
    }

    /**
     * Test IANA HTML parsing.
     */
    public function test_iana_html_parsing(): void
    {
        $mockHtml = $this->getMockIanaResponse('.pk', 'whois.pknic.net.pk');

        // Use reflection to test private method
        $reflection = new \ReflectionClass(EnhancedWhoisHelper::class);
        $method = $reflection->getMethod('fetchIanaWhoisServer');
        $method->setAccessible(true);

        // Mock HTTP response
        Http::fake([
            'https://www.iana.org/domains/root/db/pk.html' => Http::response($mockHtml),
        ]);

        $result = $method->invoke(null, '.pk');
        $this->assertEquals('whois.pknic.net.pk', $result);
    }

    /**
     * Test handling of failed IANA requests.
     */
    public function test_failed_iana_requests(): void
    {
        // Mock failed HTTP response
        Http::fake([
            'https://www.iana.org/domains/root/db/invalid.html' => Http::response('Not found', 404),
        ]);

        // Use reflection to test private method
        $reflection = new \ReflectionClass(EnhancedWhoisHelper::class);
        $method = $reflection->getMethod('fetchIanaWhoisServer');
        $method->setAccessible(true);

        $result = $method->invoke(null, '.invalid');
        $this->assertNull($result);
    }

    /**
     * Test domain validation integration.
     */
    public function test_domain_validation_integration(): void
    {
        // Test with invalid domain
        $result = EnhancedWhoisHelper::performWhoisLookup('invalid-domain');
        $this->assertNull($result);

        // Test with empty domain
        $result = EnhancedWhoisHelper::performWhoisLookup('');
        $this->assertNull($result);
    }

    /**
     * Get mock IANA response HTML.
     */
    private function getMockIanaResponse(string $tld, string $whoisServer): string
    {
        return '
        <div id="body">
            <article class="hemmed sidenav">
                <main>
                    <h1>Delegation Record for '.strtoupper(ltrim($tld, '.')).'</h1>
                    <p>(Country-code top-level domain)</p>
                    
                    <h2>Registry Information</h2>
                    <p>
                        <b>URL for registration services:</b> <a href="http://example.com/">http://example.com/</a><br>
                        <b>WHOIS Server:</b> '.$whoisServer.' <br>
                    </p>
                </main>
            </article>
        </div>';
    }
}
