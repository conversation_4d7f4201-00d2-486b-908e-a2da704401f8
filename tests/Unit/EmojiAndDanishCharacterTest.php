<?php

namespace Tests\Unit;

use App\Helpers\WebsiteCompanyHelper;
use App\Models\Lead;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmojiAndDanishCharacterTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test at danske karakterer (æøå) håndteres korrekt.
     */
    public function test_danish_characters_support(): void
    {
        $danishText = 'Køb helsekost på nettet - Åben alle dage! Ægte kvalitet.';

        // Test UTF-8 konvertering
        $result = WebsiteCompanyHelper::ensureUtf8($danishText);
        $this->assertEquals($danishText, $result);
        $this->assertTrue(mb_check_encoding($result, 'UTF-8'));

        // Test database lagring
        $lead = Lead::factory()->create([
            'website_title' => $danishText,
            'domain_owner' => '<PERSON><PERSON><PERSON>',
            'comment' => 'Specialiseret i økologiske produkter på Sjælland',
        ]);

        $this->assertDatabaseHas('leads', [
            'id' => $lead->id,
            'website_title' => $danishText,
            'domain_owner' => 'Jørgen Sørensen',
            'comment' => 'Specialiseret i økologiske produkter på Sjælland',
        ]);
    }

    /**
     * Test at emojis understøttes fuldt ud.
     */
    public function test_emoji_support(): void
    {
        $emojiText = '🚀 Hurtig levering! 😀 Glade kunder! 💪 Stærke produkter! 🇩🇰 Dansk kvalitet!';

        // Test UTF-8 konvertering
        $result = WebsiteCompanyHelper::ensureUtf8($emojiText);
        $this->assertEquals($emojiText, $result);
        $this->assertTrue(mb_check_encoding($result, 'UTF-8'));

        // Test database lagring
        $lead = Lead::factory()->create([
            'website_title' => $emojiText,
            'website_meta_description' => '🎉 Velkommen til vores webshop! 🛒 Shop nu!',
            'comment' => '⭐ 5-stjernet service ⭐',
        ]);

        $this->assertDatabaseHas('leads', [
            'id' => $lead->id,
            'website_title' => $emojiText,
            'website_meta_description' => '🎉 Velkommen til vores webshop! 🛒 Shop nu!',
            'comment' => '⭐ 5-stjernet service ⭐',
        ]);
    }

    /**
     * Test blanding af danske karakterer og emojis.
     */
    public function test_mixed_danish_and_emoji_support(): void
    {
        $mixedText = '🇩🇰 Køb ægte danske produkter på nettet! 😊 Åben alle dage! 🚚 Hurtig levering til hele Sjælland og Jylland! 💯';

        // Test UTF-8 konvertering
        $result = WebsiteCompanyHelper::ensureUtf8($mixedText);
        $this->assertEquals($mixedText, $result);
        $this->assertTrue(mb_check_encoding($result, 'UTF-8'));

        // Test database lagring
        $lead = Lead::factory()->create([
            'website_title' => $mixedText,
            'domain_owner' => '🏢 Jørgen & Søn ApS',
            'website_powered_by' => '💻 Webløsninger.dk',
            'comment' => '📞 Ring på 89889557 for hjælp! 🕐 Åben 9-17 på hverdage',
        ]);

        $this->assertDatabaseHas('leads', [
            'id' => $lead->id,
            'website_title' => $mixedText,
            'domain_owner' => '🏢 Jørgen & Søn ApS',
            'website_powered_by' => '💻 Webløsninger.dk',
            'comment' => '📞 Ring på 89889557 for hjælp! 🕐 Åben 9-17 på hverdage',
        ]);
    }

    /**
     * Test internationale karakterer.
     */
    public function test_international_characters_support(): void
    {
        $internationalText = 'Café résumé naïve piñata Zürich François Müller';

        // Test UTF-8 konvertering
        $result = WebsiteCompanyHelper::ensureUtf8($internationalText);
        $this->assertEquals($internationalText, $result);
        $this->assertTrue(mb_check_encoding($result, 'UTF-8'));

        // Test database lagring
        $lead = Lead::factory()->create([
            'website_title' => $internationalText,
            'domain_owner' => 'François Müller GmbH',
            'comment' => 'Spécialisé en produits européens',
        ]);

        $this->assertDatabaseHas('leads', [
            'id' => $lead->id,
            'website_title' => $internationalText,
            'domain_owner' => 'François Müller GmbH',
            'comment' => 'Spécialisé en produits européens',
        ]);
    }

    /**
     * Test at problematiske byte sekvenser konverteres korrekt.
     */
    public function test_problematic_byte_sequences(): void
    {
        // Simuler den originale fejl: '\xF8b hel...'
        $problematicString = "K\xF8b helsekost online"; // ø som ISO-8859-1 byte

        $result = WebsiteCompanyHelper::ensureUtf8($problematicString);

        $this->assertNotNull($result);
        $this->assertTrue(mb_check_encoding($result, 'UTF-8'));
        $this->assertStringContainsString('Køb', $result);
        $this->assertStringContainsString('helsekost', $result);

        // Test at det kan gemmes i databasen uden fejl
        $lead = Lead::factory()->create([
            'website_title' => $result,
        ]);

        $this->assertNotNull($lead->website_title);
        $this->assertTrue(mb_check_encoding($lead->website_title, 'UTF-8'));
    }
}
