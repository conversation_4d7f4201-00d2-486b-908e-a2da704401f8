<?php

use App\Jobs\GetWebsiteWhoisJob;

describe('GetWebsiteWhoisJob', function () {
    it('includes domain and lookup domain information in error logs', function () {
        // Test that the job class has the enhanced logging methods
        $job = new GetWebsiteWhoisJob(12345);

        // Verify the job class exists and can be instantiated
        expect($job)->toBeInstanceOf(GetWebsiteWhoisJob::class);

        // Verify the job has the expected properties
        $reflection = new ReflectionClass($job);
        expect($reflection->hasProperty('leadId'))->toBeTrue();
        expect($reflection->hasProperty('tries'))->toBeTrue();
        expect($reflection->hasProperty('backoff'))->toBeTrue();
    });

    it('can handle IDN domain conversion for logging', function () {
        // Test IDN conversion functionality that's used in the job
        $unicodeDomain = 'münchen.de';
        $asciiDomain = idn_to_ascii($unicodeDomain, IDNA_DEFAULT, INTL_IDNA_VARIANT_UTS46);

        expect($asciiDomain)->toBe('xn--mnchen-3ya.de');
        expect($asciiDomain)->not->toBe($unicodeDomain);
    });

    it('handles IDN conversion failure gracefully', function () {
        // Test fallback behavior when IDN conversion fails or returns unexpected results
        $testDomain = 'test.com';
        $result = idn_to_ascii($testDomain, IDNA_DEFAULT, INTL_IDNA_VARIANT_UTS46);

        // For ASCII domains, the result should be the same or the original
        expect($result)->toBeString();

        // Test the fallback logic used in the job
        $fallbackDomain = $result === false ? $testDomain : $result;
        expect($fallbackDomain)->toBeString();
        expect(strlen($fallbackDomain))->toBeGreaterThan(0);
    });
});
