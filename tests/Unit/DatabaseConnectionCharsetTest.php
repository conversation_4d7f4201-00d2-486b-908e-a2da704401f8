<?php

namespace Tests\Unit;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class DatabaseConnectionCharsetTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test at database connection bruger korrekt charset og collation.
     */
    public function test_database_connection_charset(): void
    {
        // Check connection charset
        $charset = DB::select('SELECT @@character_set_connection as charset')[0]->charset;
        $this->assertEquals('utf8mb4', $charset, 'Database connection skal bruge utf8mb4 charset');

        // Check connection collation
        $collation = DB::select('SELECT @@collation_connection as collation')[0]->collation;
        $this->assertEquals('utf8mb4_unicode_ci', $collation, 'Database connection skal bruge utf8mb4_unicode_ci collation');
    }

    /**
     * Test at vi kan indsætte danske karakterer direkte.
     */
    public function test_direct_danish_character_insertion(): void
    {
        // Test direkte SQL insertion med danske karakterer
        $danishText = 'Køb helsekost på nettet - Åben alle dage! Ægte kvalitet.';

        DB::statement('
            INSERT INTO leads (website_title, created_at, updated_at) 
            VALUES (?, NOW(), NOW())
        ', [$danishText]);

        // Verify data was stored correctly
        $result = DB::select('SELECT website_title FROM leads WHERE website_title = ?', [$danishText]);

        $this->assertCount(1, $result);
        $this->assertEquals($danishText, $result[0]->website_title);
    }

    /**
     * Test at vi kan indsætte emojis direkte.
     */
    public function test_direct_emoji_insertion(): void
    {
        // Test direkte SQL insertion med emojis
        $emojiText = '🚀 Hurtig levering! 😀 Glade kunder! 🇩🇰 Dansk kvalitet!';

        DB::statement('
            INSERT INTO leads (website_title, created_at, updated_at) 
            VALUES (?, NOW(), NOW())
        ', [$emojiText]);

        // Verify data was stored correctly
        $result = DB::select('SELECT website_title FROM leads WHERE website_title = ?', [$emojiText]);

        $this->assertCount(1, $result);
        $this->assertEquals($emojiText, $result[0]->website_title);
    }

    /**
     * Test at database kan håndtere 4-byte UTF-8 karakterer.
     */
    public function test_four_byte_utf8_support(): void
    {
        // Test 4-byte UTF-8 karakterer (emojis, specielle symboler)
        $fourByteText = '𝕳𝖊𝖑𝖑𝖔 𝖂𝖔𝖗𝖑𝖉! 🌟💫⭐✨🎉🎊';

        DB::statement('
            INSERT INTO leads (website_title, created_at, updated_at) 
            VALUES (?, NOW(), NOW())
        ', [$fourByteText]);

        // Verify data was stored correctly
        $result = DB::select('SELECT website_title FROM leads WHERE website_title = ?', [$fourByteText]);

        $this->assertCount(1, $result);
        $this->assertEquals($fourByteText, $result[0]->website_title);
    }

    /**
     * Test at connection settings er korrekte.
     */
    public function test_connection_settings(): void
    {
        // Test alle relevante MySQL variabler
        $variables = DB::select('
            SELECT 
                @@character_set_client as client_charset,
                @@character_set_connection as connection_charset,
                @@character_set_results as results_charset,
                @@collation_connection as connection_collation
        ')[0];

        $this->assertEquals('utf8mb4', $variables->client_charset);
        $this->assertEquals('utf8mb4', $variables->connection_charset);
        $this->assertEquals('utf8mb4', $variables->results_charset);
        $this->assertEquals('utf8mb4_unicode_ci', $variables->connection_collation);
    }
}
