<?php

namespace Tests\Unit;

use App\Jobs\GetWebsiteInformationJob;
use App\Models\Lead;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class GetWebsiteInformationJobUtf8Test extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that the job properly handles UTF-8 content from websites.
     */
    public function test_job_handles_utf8_website_content(): void
    {
        // Create a test lead
        $lead = Lead::factory()->create([
            'domain' => 'healthwell.dk',
        ]);

        // Mock HTTP response with Danish characters in ISO-8859-1 encoding
        $danishTitle = 'Køb helsekost online hos Healthwell.dk';
        $danishDescription = 'Healthwell.dk tilbyder helsekost på nettet til fantastiske priser';

        // Convert to ISO-8859-1 to simulate the problematic encoding
        $iso88591Title = mb_convert_encoding($danishTitle, 'ISO-8859-1', 'UTF-8');
        $iso88591Description = mb_convert_encoding($danishDescription, 'ISO-8859-1', 'UTF-8');

        $htmlContent = '<html><head>'.
            '<title>'.$iso88591Title.'</title>'.
            '<meta name="description" content="'.$iso88591Description.'">'.
            '</head><body>Content</body></html>';

        Http::fake([
            'http://healthwell.dk' => Http::response($htmlContent, 200),
        ]);

        // Suppress log output during test
        Log::shouldReceive('info')->andReturn(true);
        Log::shouldReceive('warning')->andReturn(true);
        Log::shouldReceive('error')->andReturn(true);

        // Execute the job
        $job = new GetWebsiteInformationJob($lead->id);
        $job->handle();

        // Refresh the lead from database
        $lead->refresh();

        // Verify that the data was saved correctly as UTF-8
        $this->assertNotNull($lead->website_title);
        $this->assertNotNull($lead->website_meta_description);

        // Verify the content is valid UTF-8
        $this->assertTrue(mb_check_encoding($lead->website_title, 'UTF-8'));
        $this->assertTrue(mb_check_encoding($lead->website_meta_description, 'UTF-8'));

        // Verify the content matches the expected Danish text
        $this->assertEquals($danishTitle, $lead->website_title);
        $this->assertEquals($danishDescription, $lead->website_meta_description);
    }

    /**
     * Test that the job handles corrupted encoding gracefully.
     */
    public function test_job_handles_corrupted_encoding_gracefully(): void
    {
        // Create a test lead
        $lead = Lead::factory()->create([
            'domain' => 'example.com',
        ]);

        // Create HTML with corrupted UTF-8 bytes (similar to the error case)
        $corruptedTitle = "K\xF8b helsekost online";
        $htmlContent = '<html><head><title>'.$corruptedTitle.'</title></head><body>Content</body></html>';

        Http::fake([
            'http://example.com' => Http::response($htmlContent, 200),
        ]);

        // Suppress log output during test
        Log::shouldReceive('info')->andReturn(true);
        Log::shouldReceive('warning')->andReturn(true);
        Log::shouldReceive('error')->andReturn(true);

        // Execute the job - should not throw an exception
        $job = new GetWebsiteInformationJob($lead->id);
        $job->handle();

        // Refresh the lead from database
        $lead->refresh();

        // Verify that some data was saved (even if cleaned up)
        if ($lead->website_title !== null) {
            $this->assertTrue(mb_check_encoding($lead->website_title, 'UTF-8'));
        }
    }

    /**
     * Test the ensureUtf8Content method directly.
     */
    public function test_ensure_utf8_content_method(): void
    {
        $lead = Lead::factory()->create(['domain' => 'test.com']);
        $job = new GetWebsiteInformationJob($lead->id);

        // Use reflection to access the private method
        $reflection = new \ReflectionClass($job);
        $method = $reflection->getMethod('ensureUtf8Content');
        $method->setAccessible(true);

        // Test valid UTF-8
        $validUtf8 = 'Køb helsekost online';
        $result = $method->invoke($job, $validUtf8);
        $this->assertEquals($validUtf8, $result);
        $this->assertTrue(mb_check_encoding($result, 'UTF-8'));

        // Test ISO-8859-1 conversion
        $iso88591String = mb_convert_encoding('Køb helsekost online', 'ISO-8859-1', 'UTF-8');
        $result = $method->invoke($job, $iso88591String);
        $this->assertEquals('Køb helsekost online', $result);
        $this->assertTrue(mb_check_encoding($result, 'UTF-8'));

        // Test corrupted encoding
        $corruptedString = "K\xF8b helsekost";
        $result = $method->invoke($job, $corruptedString);
        $this->assertTrue(mb_check_encoding($result, 'UTF-8'));
        $this->assertStringContainsString('K', $result);
    }
}
