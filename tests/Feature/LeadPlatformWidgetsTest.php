<?php

use App\Filament\Resources\LeadResource\Widgets\LeadPlatformsPieDotDK;
use App\Filament\Resources\LeadResource\Widgets\LeadPlatformsPieDotSE;
use App\Filament\Resources\LeadResource\Widgets\LeadPlatformsPieDotNO;
use App\Models\Lead;
use App\Models\User;
use Filament\Widgets\ChartWidget;

beforeEach(function () {
    $this->user = User::factory()->create();
});

it('can render LeadPlatformsPieDotDK widget', function () {
    // Create test data
    Lead::factory()->create([
        'tld' => 'dk',
        'website_platform_name' => 'Shopify',
    ]);

    Lead::factory()->create([
        'tld' => 'dk',
        'website_platform_name' => 'DanDomain - Webshop',
    ]);

    $widget = new LeadPlatformsPieDotDK();

    expect($widget)->toBeInstanceOf(ChartWidget::class);

    // Use reflection to access protected properties and methods
    $reflection = new ReflectionClass($widget);

    $headingProperty = $reflection->getProperty('heading');
    $headingProperty->setAccessible(true);
    expect($headingProperty->getValue($widget))->toBe('Platforms .dk');

    $sortProperty = $reflection->getProperty('sort');
    $sortProperty->setAccessible(true);
    expect($sortProperty->getValue($widget))->toBe(56);

    $getDataMethod = $reflection->getMethod('getData');
    $getDataMethod->setAccessible(true);
    $data = $getDataMethod->invoke($widget);

    expect($data)->toHaveKey('datasets');
    expect($data)->toHaveKey('labels');
    expect($data['datasets'][0]['label'])->toBe('Platforms .dk');
});

it('can render LeadPlatformsPieDotSE widget', function () {
    // Create test data
    Lead::factory()->create([
        'tld' => 'se',
        'website_platform_name' => 'Shopify',
    ]);

    Lead::factory()->create([
        'tld' => 'se',
        'website_platform_name' => 'Shoporama',
    ]);

    $widget = new LeadPlatformsPieDotSE();

    expect($widget)->toBeInstanceOf(ChartWidget::class);

    // Use reflection to access protected properties and methods
    $reflection = new ReflectionClass($widget);

    $headingProperty = $reflection->getProperty('heading');
    $headingProperty->setAccessible(true);
    expect($headingProperty->getValue($widget))->toBe('Platforms .se');

    $sortProperty = $reflection->getProperty('sort');
    $sortProperty->setAccessible(true);
    expect($sortProperty->getValue($widget))->toBe(57);

    $getDataMethod = $reflection->getMethod('getData');
    $getDataMethod->setAccessible(true);
    $data = $getDataMethod->invoke($widget);

    expect($data)->toHaveKey('datasets');
    expect($data)->toHaveKey('labels');
    expect($data['datasets'][0]['label'])->toBe('Platforms .se');
});

it('can render LeadPlatformsPieDotNO widget', function () {
    // Create test data
    Lead::factory()->create([
        'tld' => 'no',
        'website_platform_name' => 'Shopify',
    ]);

    Lead::factory()->create([
        'tld' => 'no',
        'website_platform_name' => 'Ideal Shop',
    ]);

    $widget = new LeadPlatformsPieDotNO();

    expect($widget)->toBeInstanceOf(ChartWidget::class);

    // Use reflection to access protected properties and methods
    $reflection = new ReflectionClass($widget);

    $headingProperty = $reflection->getProperty('heading');
    $headingProperty->setAccessible(true);
    expect($headingProperty->getValue($widget))->toBe('Platforms .no');

    $sortProperty = $reflection->getProperty('sort');
    $sortProperty->setAccessible(true);
    expect($sortProperty->getValue($widget))->toBe(58);

    $getDataMethod = $reflection->getMethod('getData');
    $getDataMethod->setAccessible(true);
    $data = $getDataMethod->invoke($widget);

    expect($data)->toHaveKey('datasets');
    expect($data)->toHaveKey('labels');
    expect($data['datasets'][0]['label'])->toBe('Platforms .no');
});

it('filters out empty and null platform names correctly', function () {
    // Create test data with various edge cases
    Lead::factory()->create([
        'tld' => 'dk',
        'website_platform_name' => 'Shopify',
    ]);

    Lead::factory()->create([
        'tld' => 'dk',
        'website_platform_name' => null,
    ]);

    Lead::factory()->create([
        'tld' => 'dk',
        'website_platform_name' => '',
    ]);

    Lead::factory()->create([
        'tld' => 'dk',
        'website_platform_name' => '0',
    ]);

    $widget = new LeadPlatformsPieDotDK();

    // Use reflection to access protected method
    $reflection = new ReflectionClass($widget);
    $getDataMethod = $reflection->getMethod('getData');
    $getDataMethod->setAccessible(true);
    $data = $getDataMethod->invoke($widget);

    // Should only have one valid platform (Shopify)
    expect(count($data['datasets'][0]['data']))->toBe(1);
    expect($data['labels'][0])->toContain('Shopify');
});

it('generates consistent colors for platform names', function () {
    $widget = new LeadPlatformsPieDotDK();
    
    // Use reflection to access the protected method
    $reflection = new ReflectionClass($widget);
    $method = $reflection->getMethod('stringToColorCode');
    $method->setAccessible(true);
    
    $color1 = $method->invoke($widget, 'Shopify');
    $color2 = $method->invoke($widget, 'Shopify');
    $color3 = $method->invoke($widget, 'DanDomain');
    
    // Same string should produce same color
    expect($color1)->toBe($color2);
    
    // Different strings should produce different colors
    expect($color1)->not->toBe($color3);
    
    // Colors should be valid hex codes
    expect($color1)->toMatch('/^#[0-9a-f]{6}$/i');
    expect($color3)->toMatch('/^#[0-9a-f]{6}$/i');
});
