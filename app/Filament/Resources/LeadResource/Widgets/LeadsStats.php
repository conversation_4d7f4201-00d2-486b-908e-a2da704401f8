<?php

namespace App\Filament\Resources\LeadResource\Widgets;

use App\Models\Lead;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;
use Illuminate\Support\Number;

class LeadsStats extends BaseWidget
{
    protected int|string|array $columnSpan = 6;

    protected function getColumns(): int
    {
        return 2;
    }

    protected function getStats(): array
    {
        return [
            Stat::make('Domains total', Number::format(Lead::count()))
                ->description('Total amount of domains')
                ->chart(
                    Trend::model(Lead::class)
                        ->between(
                            start: now()->subDays(30),
                            end: now(),
                        )
                        ->perDay()
                        ->count()
                        ->map(fn (TrendValue $value) => $value->aggregate)->toArray()
                )
                ->color('success'),
            Stat::make('Domains Active', Number::format(Lead::where('domain_status', true)->count()))
                ->description('Total Active domains')
                ->chart(
                    Trend::query(Lead::where('domain_status', true))
                        ->between(
                            start: now()->subDays(30),
                            end: now(),
                        )
                        ->perDay()
                        ->count()
                        ->map(fn (TrendValue $value) => $value->aggregate)->toArray()
                )
                ->color('success'),
        ];
    }
}
