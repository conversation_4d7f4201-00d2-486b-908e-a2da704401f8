<?php

namespace App\Filament\Resources\LeadResource\Widgets;

use App\Models\Lead;
use Filament\Widgets\ChartWidget;

class LeadGoldenPlanetPlatformPie extends ChartWidget
{
    protected static ?string $heading = 'Golden Planet TLDs';

    protected static ?int $sort = 43;

    protected int|string|array $columnSpan = 1;

    protected function getData(): array
    {
        // Retrieve TLDs directly from the database for Golden Planet leads
        $tldCounts = Lead::where('website_platform_name', 'Golden Planet')
            ->whereNotNull('tld')
            ->where('tld', '!=', '')
            ->where('tld', '!=', '0')
            ->pluck('tld')
            ->countBy()
            ->filter(function ($count, $tld) {
                return $count > 0 && $tld !== null && $tld !== '';
            })->sortDesc();

        $segmentColors = $tldCounts->keys()->map(function ($tld) {
            return $this->stringToColorCode($tld);
        })->toArray();

        $chartLabels = $tldCounts->map(function ($count, $tld) {
            return "{$tld} ({$count})";
        })->values()->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'TLDs for Golden Planet',
                    'data' => $tldCounts->values()->toArray(),
                    'backgroundColor' => $segmentColors,
                ],
            ],
            'labels' => $chartLabels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }

    protected function stringToColorCode(string $string): string
    {
        // Create a hash of the string
        $hash = md5($string);

        // Use the first 6 characters of the hash as a hex color code
        $colorCode = '#'.substr($hash, 0, 6);

        return $colorCode;
    }
}
