<?php

namespace App\Filament\Resources\LeadResource\Widgets;

use App\Models\Lead;
use Filament\Widgets\ChartWidget;

class LeadPlatformsPieDotDK extends ChartWidget
{
    protected static ?string $heading = 'Platforms .dk';

    protected static ?int $sort = 56;

    protected int | string | array $columnSpan = 1;

    /**
     * Generate a consistent color code for a given string.
     */
    protected function stringToColorCode($string): string
    {
        $hash = md5($string);

        return '#'.substr($hash, 0, 6);
    }

    /**
     * Provide data for the pie chart.
     */
    protected function getData(): array
    {
        // Retrieve platforms from Leads model, filtering by TLD '.dk'
        $platforms = Lead::where('tld', 'dk')->pluck('website_platform_name');

        // Count occurrences of each platform
        $platformCounts = $platforms->countBy()->filter(function ($count, $platform) {
            return $count > 0 && $platform !== null && $platform !== '' && $platform !== '0';
        })->sortDesc();

        // Generate colors for each platform using the hashing function
        $segmentColors = $platformCounts->keys()->map(function ($platform) {
            return $this->stringToColorCode($platform);
        })->toArray();

        // Prepare labels with counts
        $chartLabels = $platformCounts->map(function ($count, $platform) {
            return "{$platform} ({$count})";
        })->values()->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'Platforms .dk',
                    'data' => $platformCounts->values()->toArray(),
                    'backgroundColor' => $segmentColors,
                ],
            ],
            'labels' => $chartLabels,
        ];
    }

    /**
     * Specify the type of chart.
     */
    protected function getType(): string
    {
        return 'pie';
    }
}
