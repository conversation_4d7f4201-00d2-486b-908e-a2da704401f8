<?php

namespace App\Filament\Resources\LeadResource\Widgets;

use App\Models\Lead;
use Filament\Widgets\ChartWidget;

class LeadDanDomainWebshopPlatformPie extends ChartWidget
{
    protected static ?string $heading = 'DanDomain Webshop TLDs';

    protected static ?int $sort = 30;

    protected int | string | array $columnSpan = 1;

    protected function stringToColorCode($string): string
    {
        $hash = md5($string);

        return '#'.substr($hash, 0, 6);
    }

    protected function getData(): array
    {
        // Retrieve TLDs directly from the database for Dandomain leads
        $tldCounts = Lead::where('website_platform_name', 'Dandomain - Webshop')
            ->whereNotNull('tld')
            ->where('tld', '!=', '')
            ->where('tld', '!=', '0') // Assuming '0' is not a valid TLD
            ->pluck('tld')
            ->countBy()
            ->filter(function ($count, $tld) {
                return $count > 0 && $tld !== null && $tld !== '';
            })->sortDesc();

        $segmentColors = $tldCounts->keys()->map(function ($tld) {
            return $this->stringToColorCode($tld);
        })->toArray();

        $chartLabels = $tldCounts->map(function ($count, $tld) {
            return "{$tld} ({$count})";
        })->values()->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'TLDs for DanDomain Webshop',
                    'data' => $tldCounts->values()->toArray(),
                    'backgroundColor' => $segmentColors,
                ],
            ],
            'labels' => $chartLabels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
