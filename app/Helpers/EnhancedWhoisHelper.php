<?php

namespace App\Helpers;

use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Iodev\Whois\Factory as WhoisFactory;
use Iodev\Whois\Modules\Tld\TldServer;
use Iodev\Whois\Whois;

class EnhancedWhoisHelper
{
    /**
     * Socket timeout in seconds
     */
    const SOCKET_TIMEOUT = 30;

    /**
     * Cache duration for IANA WHOIS server data (24 hours)
     */
    const IANA_CACHE_DURATION = 86400;

    /**
     * Custom TLD servers that are not in the default WHOIS library
     * https://www.iana.org/domains/root/db/xx.html
     */
    private static array $customTldServers = [
        '.pk' => 'whois.pknic.net.pk',
        '.in' => 'whois.nixiregistry.in',
        '.gr' => 'www.gr',
    ];

    /**
     * Create a WHOIS instance with custom TLD servers.
     */
    public static function createWhoisInstance(): Whois
    {
        $whois = WhoisFactory::get()->createWhois();

        // Add custom TLD servers
        $customServers = [];
        foreach (self::$customTldServers as $tld => $host) {
            $customServers[] = TldServer::fromData([
                'zone' => $tld,
                'host' => $host,
            ]);
        }

        // Add dynamically discovered servers from IANA
        $dynamicServers = self::getIanaWhoisServers();
        foreach ($dynamicServers as $tld => $host) {
            if (! isset(self::$customTldServers[$tld])) {
                $customServers[] = TldServer::fromData([
                    'zone' => $tld,
                    'host' => $host,
                ]);
            }
        }

        if (! empty($customServers)) {
            $whois->getTldModule()->addServers($customServers);
            Log::info('[EnhancedWhoisHelper] Added '.count($customServers).' custom TLD servers');
        }

        return $whois;
    }

    /**
     * Get WHOIS servers from IANA database with caching.
     */
    private static function getIanaWhoisServers(): array
    {
        return Cache::remember('iana_whois_servers', self::IANA_CACHE_DURATION, function () {
            $servers = [];

            // List of TLDs to check
            $tlds = ['.pk', '.in', '.bd', '.lk', '.np', '.bt', '.mv'];

            foreach ($tlds as $tld) {
                try {
                    $whoisServer = self::fetchIanaWhoisServer($tld);
                    if ($whoisServer) {
                        $servers[$tld] = $whoisServer;
                        Log::info("[EnhancedWhoisHelper] Found IANA WHOIS server for {$tld}: {$whoisServer}");
                    }
                } catch (Exception $e) {
                    Log::warning("[EnhancedWhoisHelper] Failed to fetch IANA data for {$tld}: ".$e->getMessage());
                }
            }

            return $servers;
        });
    }

    /**
     * Fetch WHOIS server for a TLD from IANA database.
     */
    private static function fetchIanaWhoisServer(string $tld): ?string
    {
        $tldWithoutDot = ltrim($tld, '.');
        $url = "https://www.iana.org/domains/root/db/{$tldWithoutDot}.html";

        try {
            $response = Http::timeout(10)->get($url);

            if (! $response->successful()) {
                return null;
            }

            $html = $response->body();

            // Extract WHOIS server using regex
            // Looking for: <b>WHOIS Server:</b> whois.example.com <br>
            if (preg_match('/<b>WHOIS Server:<\/b>\s*([^\s<]+)\s*<br>/i', $html, $matches)) {
                return trim($matches[1]);
            }

            return null;
        } catch (Exception $e) {
            Log::warning("[EnhancedWhoisHelper] HTTP request failed for IANA {$tld}: ".$e->getMessage());

            return null;
        }
    }

    /**
     * Perform WHOIS lookup with enhanced TLD support.
     */
    public static function performWhoisLookup(string $domain): ?object
    {
        if (! WhoisHelper::isValidDomain($domain)) {
            Log::error("[EnhancedWhoisHelper] Invalid domain format: {$domain}");

            return null;
        }

        // Convert IDN domain to ASCII for WHOIS lookup
        $domainForWhois = idn_to_ascii($domain, IDNA_DEFAULT, INTL_IDNA_VARIANT_UTS46);
        if ($domainForWhois === false) {
            $domainForWhois = $domain;
        }

        Log::info("[EnhancedWhoisHelper] Performing WHOIS lookup for domain: {$domain} (ASCII: {$domainForWhois})");

        $originalTimeout = ini_get('default_socket_timeout');
        try {
            ini_set('default_socket_timeout', self::SOCKET_TIMEOUT);

            $whois = self::createWhoisInstance();
            $info = $whois->loadDomainInfo($domainForWhois);

            if ($info) {
                Log::info("[EnhancedWhoisHelper] Successfully retrieved WHOIS info for: {$domain}");

                return $info;
            } else {
                Log::warning("[EnhancedWhoisHelper] No WHOIS info found for: {$domain}");

                return null;
            }
        } catch (Exception $e) {
            Log::error("[EnhancedWhoisHelper] WHOIS lookup failed for {$domain}: ".$e->getMessage());
            throw $e;
        } finally {
            ini_set('default_socket_timeout', $originalTimeout);
        }
    }

    /**
     * Get available TLD servers (for debugging/monitoring).
     */
    public static function getAvailableTldServers(): array
    {
        $whois = self::createWhoisInstance();
        $servers = [];

        // Get default servers
        $tldModule = $whois->getTldModule();
        foreach ($tldModule->getServers() as $server) {
            $servers[$server->getZone()] = $server->getHost();
        }

        return $servers;
    }

    /**
     * Clear IANA cache (useful for testing or manual refresh).
     */
    public static function clearIanaCache(): void
    {
        Cache::forget('iana_whois_servers');
        Log::info('[EnhancedWhoisHelper] IANA WHOIS servers cache cleared');
    }

    /**
     * Add a custom TLD server at runtime.
     */
    public static function addCustomTldServer(string $tld, string $whoisHost): void
    {
        self::$customTldServers[$tld] = $whoisHost;
        Log::info("[EnhancedWhoisHelper] Added custom TLD server: {$tld} -> {$whoisHost}");
    }

    /**
     * Get all custom TLD servers.
     */
    public static function getCustomTldServers(): array
    {
        return self::$customTldServers;
    }
}
