<?php

namespace App\Helpers;

use App\Models\Lead;
use Exception;
use Illuminate\Support\Facades\Log;

class WhoisHelper
{
    /**
     * Socket timeout in seconds
     */
    const SOCKET_TIMEOUT = 30;

    /**
     * Validate domain format.
     *
     * @param  string  $domain  Domain to validate.
     * @return bool Whether domain is valid.
     */
    public static function isValidDomain(string $domain): bool
    {
        // Trim and convert to lowercase
        $domain = strtolower(trim($domain));

        // Check if domain is empty or too long
        if (empty($domain) || strlen($domain) > 255) {
            return false;
        }

        // Convert internationalized domain names (IDN) to ASCII (Punycode)
        // This handles domains like "unilån.dk" -> "xn--uniln-gra.dk"
        $asciiDomain = idn_to_ascii($domain, IDNA_DEFAULT, INTL_IDNA_VARIANT_UTS46);

        // If IDN conversion fails, try with the original domain
        if ($asciiDomain === false) {
            $asciiDomain = $domain;
        }

        // Enhanced domain validation regex
        // Checks for valid domain format with at least one dot and valid TLD
        // Supports ASCII domains and converted IDN domains
        $pattern = '/^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i';

        return (bool) preg_match($pattern, $asciiDomain);
    }

    /**
     * Fetch and update WHOIS information for a lead.
     *
     * @param  Lead  $lead  The lead model instance.
     *
     * @throws Exception When WHOIS operations fail or other critical errors occur.
     */
    public static function fetchAndUpdateWhoisInfo(Lead $lead): void
    {
        if (empty($lead->domain)) {
            Log::error('[WhoisHelper] Domain is empty for lead ID: '.$lead->id);

            return;
        }

        if (! self::isValidDomain($lead->domain)) {
            Log::error('[WhoisHelper] Invalid domain format for lead ID: '.$lead->id.' - Domain: '.$lead->domain);
            $lead->domain_status = 0; // Mark as unavailable or invalid
            $lead->save();

            return;
        }

        // Convert IDN domain to ASCII for WHOIS lookup
        $domainForWhois = idn_to_ascii($lead->domain, IDNA_DEFAULT, INTL_IDNA_VARIANT_UTS46);
        if ($domainForWhois === false) {
            $domainForWhois = $lead->domain; // Fallback to original if conversion fails
        }

        Log::info('[WhoisHelper] Performing WHOIS lookup for domain: '.$lead->domain.' (ASCII: '.$domainForWhois.') (Lead ID: '.$lead->id.')');

        try {
            // Use enhanced WHOIS helper with custom TLD support
            $info = EnhancedWhoisHelper::performWhoisLookup($domainForWhois);

            if ($info) {
                $lead->domain_status = 1; // Domain is available/active
                $lead->domain_created = $info->creationDate ? date('Y-m-d H:i:s', $info->creationDate) : null;
                $lead->domain_expires = $info->expirationDate ? date('Y-m-d H:i:s', $info->expirationDate) : null;
                $lead->domain_owner = $info->owner ?? 'Unknown Owner';
                $lead->save();
                Log::info('[WhoisHelper] Successfully updated WHOIS information for domain: '.$lead->domain);
            } else {
                $lead->domain_status = 0; // Domain not found or not available
                $lead->save();
                Log::warning('[WhoisHelper] Domain not found in WHOIS for lead ID: '.$lead->id.' - Domain: '.$lead->domain);
            }
        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
            $errorType = self::categorizeError($e);

            Log::error('[WhoisHelper] WHOIS lookup failed for domain: '.$lead->domain.' with error: '.$errorMessage, [
                'error_type' => $errorType,
                'lead_id' => $lead->id,
                'domain' => $lead->domain,
                'exception_class' => get_class($e),
            ]);

            // Handle different error types appropriately
            if ($errorType === 'database_error') {
                // For database errors, try to save with truncated data
                self::handleDatabaseError($lead, $e);
            } else {
                // For network/timeout errors, just mark as unavailable
                $lead->domain_status = 0;
                $lead->save();
            }

            // Only re-throw for non-database errors to allow job retries for network issues
            if ($errorType !== 'database_error') {
                throw $e;
            }
        }
    }

    /**
     * Categorize the type of error for appropriate handling.
     *
     * @param  Exception  $e  The exception to categorize
     * @return string The error category
     */
    private static function categorizeError(Exception $e): string
    {
        $errorMessage = $e->getMessage();

        // Database-related errors
        if (str_contains($errorMessage, 'SQLSTATE') ||
            str_contains($errorMessage, 'Data too long for column') ||
            str_contains($errorMessage, 'String data, right truncated')) {
            return 'database_error';
        }

        // Network/timeout errors
        if (str_contains($errorMessage, 'Connection timed out') ||
            str_contains($errorMessage, 'timeout') ||
            str_contains($errorMessage, 'Connection refused') ||
            str_contains($errorMessage, 'Network is unreachable')) {
            return 'network_error';
        }

        // WHOIS-specific errors
        if (str_contains($errorMessage, 'WHOIS') ||
            str_contains($errorMessage, 'No whois server')) {
            return 'whois_error';
        }

        return 'unknown_error';
    }

    /**
     * Handle database errors by attempting to save with truncated data.
     *
     * @param  Lead  $lead  The lead model instance
     * @param  Exception  $e  The database exception
     */
    private static function handleDatabaseError(Lead $lead, Exception $e): void
    {
        $errorMessage = $e->getMessage();

        // Check if it's a domain_owner column length issue
        if (str_contains($errorMessage, 'domain_owner') &&
            str_contains($errorMessage, 'Data too long for column')) {

            // Truncate domain_owner to fit the column (assuming 255 chars after migration)
            $originalOwner = $lead->domain_owner;
            $lead->domain_owner = substr($originalOwner, 0, 250).'...'; // Leave room for ellipsis

            try {
                $lead->save();
                Log::warning('[WhoisHelper] Truncated domain_owner for lead ID: '.$lead->id.' - Original: "'.$originalOwner.'" - Truncated: "'.$lead->domain_owner.'"');
            } catch (Exception $retryException) {
                Log::error('[WhoisHelper] Failed to save lead even with truncated data for lead ID: '.$lead->id.' - Error: '.$retryException->getMessage());
                // Set domain_status to 0 and try to save without domain_owner
                $lead->domain_status = 0;
                $lead->domain_owner = 'Error: Data too long';
                $lead->save();
            }
        } else {
            // For other database errors, just mark as unavailable
            $lead->domain_status = 0;
            $lead->save();
        }
    }
}
