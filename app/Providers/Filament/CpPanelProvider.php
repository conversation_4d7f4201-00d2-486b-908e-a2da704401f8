<?php

namespace App\Providers\Filament;

use App\Filament\Pages\Dashboard;
use App\Filament\Resources\LeadResource\Widgets\LeadDanDomainClassicPlatformPie;
use App\Filament\Resources\LeadResource\Widgets\LeadDanDomainHostedCMSPlatformPie;
use App\Filament\Resources\LeadResource\Widgets\LeadDanDomainWebshopPlatformPie;
use App\Filament\Resources\LeadResource\Widgets\LeadGoldenPlanetPlatformPie;
use App\Filament\Resources\LeadResource\Widgets\LeadIdealShopPlatformPie;
use App\Filament\Resources\LeadResource\Widgets\LeadPlatformsPie;
use App\Filament\Resources\LeadResource\Widgets\LeadPlatformsPieDotDK;
use App\Filament\Resources\LeadResource\Widgets\LeadPlatformsPieDotNO;
use App\Filament\Resources\LeadResource\Widgets\LeadPlatformsPieDotSE;
use App\Filament\Resources\LeadResource\Widgets\LeadSalectoPlatformPie;
use App\Filament\Resources\LeadResource\Widgets\LeadsChart;
use App\Filament\Resources\LeadResource\Widgets\LeadShopifyPlatformPie;
use App\Filament\Resources\LeadResource\Widgets\LeadShoporamaPlatformPie;
use App\Filament\Resources\LeadResource\Widgets\LeadsStats;
use App\Filament\Resources\LeadResource\Widgets\LeadTLDsPie;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class CpPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('cp')
            ->path('cp')
            ->login()
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                LeadsStats::class,
                LeadsChart::class,
                LeadTLDsPie::class,
                LeadPlatformsPie::class,

                // Platforms .dk, .se, .no on same line
                LeadPlatformsPieDotDK::class,
                LeadPlatformsPieDotSE::class,
                LeadPlatformsPieDotNO::class,

                // DanDomain widgets on same line
                LeadDanDomainWebshopPlatformPie::class,
                LeadDanDomainClassicPlatformPie::class,
                LeadDanDomainHostedCMSPlatformPie::class,

                // Shoporama, Ideal, Selecto on same line
                LeadShoporamaPlatformPie::class,
                LeadIdealShopPlatformPie::class,
                LeadSalectoPlatformPie::class,
                LeadGoldenPlanetPlatformPie::class,

                // Shopify below and full width
                LeadShopifyPlatformPie::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
