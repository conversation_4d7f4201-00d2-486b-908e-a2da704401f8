# Enhanced WHOIS Helper

## Overview

The `EnhancedWhoisHelper` provides advanced WHOIS lookup functionality with support for custom TLD servers and automatic IANA server discovery. This helper extends the basic WHOIS functionality to support domains from Pakistan (.pk), India (.in), and other regions that may not be included in the default WHOIS library.

## Features

### 🌐 Custom TLD Server Support
- Pre-configured support for `.pk` (Pakistan) and `.in` (India) domains
- Runtime addition of custom TLD servers
- Automatic fallback to default WHOIS servers

### 🔍 IANA Server Discovery
- Automatic discovery of WHOIS servers from IANA database
- Caching of IANA data for 24 hours to reduce API calls
- Support for multiple TLD lookups

### ⚡ Performance Optimizations
- Intelligent caching system
- Timeout management
- UTF-8 safe domain handling

## Usage

### Basic WHOIS Lookup

```php
use App\Helpers\EnhancedWhoisHelper;

// Perform WHOIS lookup with enhanced TLD support
$info = EnhancedWhoisHelper::performWhoisLookup('example.pk');

if ($info) {
    echo "Domain: " . $info->domainName . "\n";
    echo "Owner: " . ($info->owner ?? 'N/A') . "\n";
    echo "Created: " . ($info->creationDate ? date('Y-m-d', $info->creationDate) : 'N/A') . "\n";
    echo "Expires: " . ($info->expirationDate ? date('Y-m-d', $info->expirationDate) : 'N/A') . "\n";
}
```

### Creating Custom WHOIS Instance

```php
// Create WHOIS instance with all custom servers loaded
$whois = EnhancedWhoisHelper::createWhoisInstance();
$info = $whois->loadDomainInfo('example.pk');
```

### Adding Custom TLD Servers

```php
// Add a custom TLD server at runtime
EnhancedWhoisHelper::addCustomTldServer('.bd', 'whois.btcl.net.bd');

// Get all custom servers
$customServers = EnhancedWhoisHelper::getCustomTldServers();
```

### Cache Management

```php
// Clear IANA cache to force refresh
EnhancedWhoisHelper::clearIanaCache();

// Get available TLD servers (for debugging)
$servers = EnhancedWhoisHelper::getAvailableTldServers();
```

## Artisan Commands

### List Available Servers
```bash
php artisan whois:server list
```

### Test Domain Lookup
```bash
php artisan whois:server test example.pk
php artisan whois:server test google.in
```

### Clear IANA Cache
```bash
php artisan whois:server clear-cache
```

### Add Custom Server
```bash
php artisan whois:server add .bd whois.btcl.net.bd
```

## Supported TLDs

### Pre-configured Custom TLDs
- `.pk` - Pakistan (whois.pknic.net.pk)
- `.in` - India (whois.registry.in)

### IANA Auto-Discovery
The helper automatically discovers WHOIS servers for these TLDs from IANA:
- `.pk` - Pakistan
- `.in` - India  
- `.bd` - Bangladesh
- `.lk` - Sri Lanka
- `.np` - Nepal
- `.bt` - Bhutan
- `.mv` - Maldives

### Adding New TLDs

To add permanent support for a new TLD, update the `$customTldServers` array in `EnhancedWhoisHelper.php`:

```php
private static array $customTldServers = [
    '.pk' => 'whois.pknic.net.pk',
    '.in' => 'whois.registry.in',
    '.bd' => 'whois.btcl.net.bd',  // Add new TLD here
];
```

## IANA Integration

The helper automatically fetches WHOIS server information from IANA's database:

### How it works:
1. Fetches HTML from `https://www.iana.org/domains/root/db/{tld}.html`
2. Parses the HTML to extract WHOIS server information
3. Caches the results for 24 hours
4. Falls back gracefully if IANA is unavailable

### Example IANA Response Parsing:
```html
<h2>Registry Information</h2>
<p>
    <b>WHOIS Server:</b> whois.pknic.net.pk <br>
</p>
```

## Error Handling

The helper includes comprehensive error handling:

### Network Errors
- Timeout management with configurable socket timeout
- Graceful fallback when IANA is unavailable
- Retry logic for transient failures

### Domain Validation
- UTF-8 safe domain validation
- IDN (Internationalized Domain Names) support
- Punycode conversion for international domains

### Logging
All operations are logged with appropriate levels:
- `INFO`: Successful operations and server additions
- `WARNING`: Failed IANA lookups or missing WHOIS data
- `ERROR`: Critical failures and invalid domains

## Integration with Existing Code

The `WhoisHelper` class has been updated to use `EnhancedWhoisHelper`:

```php
// Old code:
$whois = WhoisFactory::get()->createWhois();
$info = $whois->loadDomainInfo($domain);

// New code (automatic):
$info = EnhancedWhoisHelper::performWhoisLookup($domain);
```

## Performance Considerations

### Caching Strategy
- IANA data cached for 24 hours
- Cache key: `iana_whois_servers`
- Automatic cache warming on first request

### Timeout Management
- Socket timeout: 30 seconds (configurable)
- HTTP timeout for IANA requests: 10 seconds
- Graceful degradation on timeout

### Memory Usage
- Minimal memory footprint
- Servers loaded on-demand
- Efficient array storage for TLD mappings

## Testing

Run the test suite:
```bash
php artisan test tests/Unit/EnhancedWhoisHelperTest.php
```

Test specific functionality:
```bash
# Test custom TLD servers
php artisan test --filter=test_custom_tld_servers_configuration

# Test IANA integration
php artisan test --filter=test_iana_cache_functionality

# Test domain validation
php artisan test --filter=test_domain_validation_integration
```

## Monitoring and Debugging

### Check Available Servers
```bash
php artisan whois:server list
```

### Test Specific Domains
```bash
php artisan whois:server test example.pk
php artisan whois:server test google.in
```

### Monitor Logs
```bash
tail -f storage/logs/laravel.log | grep "EnhancedWhoisHelper"
```

## Future Enhancements

### Planned Features
1. **Database Storage**: Store discovered WHOIS servers in database
2. **Health Monitoring**: Regular health checks for WHOIS servers
3. **Rate Limiting**: Implement rate limiting for WHOIS requests
4. **Bulk Operations**: Support for bulk domain lookups
5. **API Integration**: REST API endpoints for WHOIS data

### Contributing
To add support for new TLDs:
1. Research the WHOIS server for the TLD
2. Add it to `$customTldServers` array
3. Test with real domains
4. Update documentation
5. Add test cases
